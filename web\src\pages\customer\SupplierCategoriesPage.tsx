import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Search, MapPin, Store, Utensils, Shirt, Pill, ShoppingCart,
  Sparkles, Star, Clock, ChevronRight, X, Crown, Shield, ArrowLeft,
  AlertCircle, RefreshCw
} from 'lucide-react';
import { apiService, type Category, comprehensiveSearch, type ComprehensiveSearchResults } from '../../services/api';

// Enhanced categories interface for web display
interface WebCategory {
  key: string;
  label: string;
  icon: any;
  bgGradient: string;
  shadowColor: string;
  route: string;
  subtitle: string;
  badge: string;
  badgeColor: string;
  stats: { suppliers: number; avgRating: number; deliveryTime: string };
  special: boolean;
  _id: string;
  color: string;
  description?: string;
  isActive: boolean;
  order: number;
}

// Dynamic icon mapping based on category key or icon field from backend
function getIconForCategory(category: Category) {
  // If backend provides icon field, try to map it to actual icon components
  if (category.icon) {
    const iconMap: Record<string, any> = {
      'fast-food-outline': Utensils,
      'utensils': Utensils,
      'restaurant': Utensils,
      'food': Utensils,
      'shirt-outline': Shirt,
      'shirt': Shirt,
      'clothing': Shirt,
      'fashion': Shirt,
      'medkit-outline': Pill,
      'medkit': Pill,
      'pill': Pill,
      'pharmacy': Pill,
      'health': Pill,
      'cart-outline': ShoppingCart,
      'cart': ShoppingCart,
      'shopping-cart': ShoppingCart,
      'supermarket': ShoppingCart,
      'grocery': ShoppingCart,
      'store': Store,
      'shop': Store
    };

    if (iconMap[category.icon]) {
      return iconMap[category.icon];
    }
  }

  // Fallback to key-based mapping for backward compatibility
  const keyBasedIcons: Record<string, any> = {
    'restaurants': Utensils,
    'clothings': Shirt,
    'pharmacies': Pill,
    'supermarkets': ShoppingCart
  };

  return keyBasedIcons[category.key] || Store;
}

// Generate gradient based on category color from backend
function getGradientForCategory(category: Category): string {
  if (category.color) {
    // Convert hex color to gradient classes
    const colorMap: Record<string, string> = {
      '#4A90E2': 'from-blue-500 via-blue-600 to-blue-700',
      '#F5A623': 'from-orange-500 via-orange-600 to-orange-700',
      '#50E3C2': 'from-green-500 via-emerald-500 to-teal-600',
      '#BD10E0': 'from-purple-500 via-purple-600 to-purple-700',
      '#FF6B6B': 'from-red-500 via-red-600 to-red-700',
      '#4ECDC4': 'from-teal-500 via-teal-600 to-teal-700',
      '#45B7D1': 'from-blue-400 via-blue-500 to-blue-600',
      '#96CEB4': 'from-green-400 via-green-500 to-green-600',
      '#FFEAA7': 'from-yellow-400 via-yellow-500 to-yellow-600',
      '#DDA0DD': 'from-purple-400 via-purple-500 to-purple-600'
    };

    if (colorMap[category.color]) {
      return colorMap[category.color];
    }

    // Generate gradient based on color characteristics
    const color = category.color.toLowerCase();
    if (color.includes('blue') || color.includes('#4')) {
      return 'from-blue-500 via-blue-600 to-blue-700';
    } else if (color.includes('green') || color.includes('#5')) {
      return 'from-green-500 via-emerald-500 to-teal-600';
    } else if (color.includes('red') || color.includes('#f')) {
      return 'from-red-500 via-red-600 to-red-700';
    } else if (color.includes('purple') || color.includes('#b')) {
      return 'from-purple-500 via-purple-600 to-purple-700';
    }
  }

  // Fallback to key-based gradients
  const keyBasedGradients: Record<string, string> = {
    'restaurants': 'from-orange-500 via-red-500 to-pink-600',
    'clothings': 'from-pink-500 via-purple-500 to-indigo-600',
    'pharmacies': 'from-green-500 via-emerald-500 to-teal-600',
    'supermarkets': 'from-blue-500 via-indigo-500 to-purple-600'
  };

  return keyBasedGradients[category.key] || 'from-gray-500 via-gray-600 to-gray-700';
}

// Generate shadow based on category color
function getShadowForCategory(category: Category): string {
  if (category.color) {
    const colorMap: Record<string, string> = {
      '#4A90E2': 'shadow-blue-500/25',
      '#F5A623': 'shadow-orange-500/25',
      '#50E3C2': 'shadow-green-500/25',
      '#BD10E0': 'shadow-purple-500/25',
      '#FF6B6B': 'shadow-red-500/25',
      '#4ECDC4': 'shadow-teal-500/25',
      '#45B7D1': 'shadow-blue-500/25',
      '#96CEB4': 'shadow-green-500/25',
      '#FFEAA7': 'shadow-yellow-500/25',
      '#DDA0DD': 'shadow-purple-500/25'
    };

    if (colorMap[category.color]) {
      return colorMap[category.color];
    }
  }

  // Fallback to key-based shadows
  const keyBasedShadows: Record<string, string> = {
    'restaurants': 'shadow-orange-500/25',
    'clothings': 'shadow-pink-500/25',
    'pharmacies': 'shadow-green-500/25',
    'supermarkets': 'shadow-blue-500/25'
  };

  return keyBasedShadows[category.key] || 'shadow-gray-500/25';
}

// Use description from backend, fallback to generated subtitle
function getSubtitleForCategory(category: Category): string {
  if (category.description) {
    return category.description;
  }

  // Fallback subtitles based on key
  const keyBasedSubtitles: Record<string, string> = {
    'restaurants': 'Delicious meals from local restaurants',
    'clothings': 'Fashion & style from top brands',
    'pharmacies': 'Health & wellness products',
    'supermarkets': 'Fresh groceries & daily essentials'
  };

  return keyBasedSubtitles[category.key] || 'Discover amazing products and services';
}

// Generate badge based on category properties
function getBadgeForCategory(category: Category): string {
  // Use badge from backend if available
  if (category.badge) {
    return category.badge;
  }

  // Generate badge based on category properties
  if (category.key === 'restaurants') return 'Popular';
  if (category.key === 'clothings') return 'Trending';
  if (category.key === 'pharmacies') return 'Essential';
  if (category.key === 'supermarkets') return 'Fresh';

  return 'New';
}

// Generate badge color based on category
function getBadgeColorForCategory(category: Category): string {
  // Use badgeColor from backend if available
  if (category.badgeColor) {
    return category.badgeColor;
  }

  // Generate based on category color or key
  if (category.color) {
    const colorMap: Record<string, string> = {
      '#4A90E2': 'bg-blue-400 text-blue-900',
      '#F5A623': 'bg-orange-400 text-orange-900',
      '#50E3C2': 'bg-green-400 text-green-900',
      '#BD10E0': 'bg-purple-400 text-purple-900',
      '#FF6B6B': 'bg-red-400 text-red-900',
      '#4ECDC4': 'bg-teal-400 text-teal-900',
      '#45B7D1': 'bg-blue-400 text-blue-900',
      '#96CEB4': 'bg-green-400 text-green-900',
      '#FFEAA7': 'bg-yellow-400 text-yellow-900',
      '#DDA0DD': 'bg-purple-400 text-purple-900'
    };

    if (colorMap[category.color]) {
      return colorMap[category.color];
    }
  }

  // Fallback based on key
  const keyBasedColors: Record<string, string> = {
    'restaurants': 'bg-orange-400 text-orange-900',
    'clothings': 'bg-pink-400 text-pink-900',
    'pharmacies': 'bg-green-400 text-green-900',
    'supermarkets': 'bg-blue-400 text-blue-900'
  };

  return keyBasedColors[category.key] || 'bg-gray-400 text-gray-900';
}

// Generate stats - this should ideally come from backend
function getStatsForCategory(category: Category): { suppliers: number; avgRating: number; deliveryTime: string } {
  // TODO: This should be fetched from backend API in the future
  // For now, provide reasonable defaults based on category
  const keyBasedStats: Record<string, { suppliers: number; avgRating: number; deliveryTime: string }> = {
    'restaurants': { suppliers: 45, avgRating: 4.8, deliveryTime: '25-35 min' },
    'clothings': { suppliers: 28, avgRating: 4.6, deliveryTime: '1-2 days' },
    'pharmacies': { suppliers: 15, avgRating: 4.9, deliveryTime: '15-25 min' },
    'supermarkets': { suppliers: 32, avgRating: 4.7, deliveryTime: '30-45 min' }
  };

  return keyBasedStats[category.key] || { suppliers: 10, avgRating: 4.5, deliveryTime: '30-60 min' };
}

const SupplierCategoriesPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);

  // API state management
  const [categories, setCategories] = useState<Category[]>([]);
  const [webCategories, setWebCategories] = useState<WebCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Search state
  const [searchResults, setSearchResults] = useState<ComprehensiveSearchResults | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);

  // Convert API categories to web format - 100% backend compatible
  const convertToWebCategories = (apiCategories: Category[]): WebCategory[] => {
    return apiCategories.map(cat => ({
      key: cat.key,
      label: cat.label,
      icon: getIconForCategory(cat),
      // Use backend fields when available, fallback to generated ones
      bgGradient: cat.bgGradient || getGradientForCategory(cat),
      shadowColor: cat.shadowColor || getShadowForCategory(cat),
      route: `/customer/suppliers-page?category=${cat.key}`,
      subtitle: cat.subtitle || cat.description || getSubtitleForCategory(cat),
      badge: cat.badge || getBadgeForCategory(cat),
      badgeColor: cat.badgeColor || getBadgeColorForCategory(cat),
      stats: getStatsForCategory(cat),
      special: cat.key === 'restaurants', // Mark restaurants as special/popular
      _id: cat._id,
      color: cat.color,
      description: cat.description,
      isActive: cat.isActive,
      order: cat.order
    }));
  };

  // Load categories from API
  useEffect(() => {
    const loadCategories = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiService.getCategories();
        if (response.success && response.data) {
          const activeCategories = response.data
            .filter(cat => cat.isActive)
            .sort((a, b) => a.order - b.order);

          setCategories(activeCategories);
          setWebCategories(convertToWebCategories(activeCategories));
        } else {
          throw new Error(response.message || 'Failed to load categories');
        }
      } catch (error) {
        console.error('Error loading categories:', error);
        setError('Failed to load categories. Please try again.');
        setCategories([]);
        setWebCategories([]);
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, []);

  // Handle scroll for header animation with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Clear previous timeout
      clearTimeout(timeoutId);

      // Debounce the header compact state change
      timeoutId = setTimeout(() => {
        // Show sliding header after scrolling past the hero section
        setIsHeaderCompact(currentScrollY > 300);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Search functionality using the same API as web customer home page
  useEffect(() => {
    const performSearch = async () => {
      if (!searchQuery.trim()) {
        setSearchResults(null);
        setShowSearchResults(false);
        return;
      }

      setIsSearching(true);
      try {
        // Use real API search but filter out services
        const results = await comprehensiveSearch(searchQuery);

        // Filter out services as requested
        const filteredResults: ComprehensiveSearchResults = {
          services: [], // Ignore services results
          categories: results.categories,
          suppliers: results.suppliers,
          products: results.products,
          total: results.categories.length + results.suppliers.length + results.products.length
        };

        setSearchResults(filteredResults);
        setShowSearchResults(true);
      } catch (error) {
        console.error('Search error:', error);
        // Fallback to local search on categories
        const localResults: ComprehensiveSearchResults = {
          services: [],
          categories: webCategories
            .filter(cat =>
              cat.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
              cat.subtitle.toLowerCase().includes(searchQuery.toLowerCase())
            )
            .map(cat => ({
              type: 'category' as const,
              id: cat._id,
              title: cat.label,
              subtitle: cat.subtitle,
              description: cat.description || cat.subtitle,
              icon: 'store',
              color: cat.color,
              route: cat.route,
              data: cat
            })),
          suppliers: [],
          products: [],
          total: 0
        };
        localResults.total = localResults.categories.length;
        setSearchResults(localResults);
        setShowSearchResults(true);
      } finally {
        setIsSearching(false);
      }
    };

    const timeoutId = setTimeout(performSearch, 300);
    return () => clearTimeout(timeoutId);
  }, [searchQuery, webCategories]);

  const filteredCategories = useMemo(() => {
    if (showSearchResults) return [];
    return webCategories;
  }, [showSearchResults, webCategories]);

  const handleCategoryPress = (route: string) => {
    navigate(route);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults(null);
    setShowSearchResults(false);
  };

  const handleBackPress = () => {
    navigate('/customer/home');
  };

  const handleRetry = async () => {
    setError(null);
    setLoading(true);
    try {
      const response = await apiService.getCategories();
      if (response.success && response.data) {
        const activeCategories = response.data
          .filter(cat => cat.isActive)
          .sort((a, b) => a.order - b.order);

        setCategories(activeCategories);
        setWebCategories(convertToWebCategories(activeCategories));
      } else {
        throw new Error(response.message || 'Failed to load categories');
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      setError('Failed to load categories. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-white/20 border-t-white rounded-full mx-auto mb-4"
          />
          <h2 className="text-white text-xl font-semibold mb-2">Loading Categories</h2>
          <p className="text-white/70">Please wait while we fetch the latest categories...</p>
        </motion.div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center max-w-md"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6"
          >
            <AlertCircle className="text-red-400" size={40} />
          </motion.div>
          <h2 className="text-white text-2xl font-bold mb-4">Oops! Something went wrong</h2>
          <p className="text-white/70 mb-6">{error}</p>
          <div className="flex gap-4 justify-center">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleRetry}
              className="px-6 py-3 bg-gradient-to-r from-orange-500 to-pink-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-200 flex items-center gap-2"
            >
              <RefreshCw size={20} />
              Try Again
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleBackPress}
              className="px-6 py-3 bg-white/10 text-white rounded-xl font-medium hover:bg-white/20 transition-all duration-200 flex items-center gap-2"
            >
              <ArrowLeft size={20} />
              Go Back
            </motion.button>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/30 to-red-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-pink-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-blue-500/30 to-cyan-600/30 rounded-full blur-3xl"
          />

          {/* Floating particles */}
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, -100, -20],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Sliding Header with Scroll Animation - Only appears when scrolling */}
        <motion.div
          className="fixed left-0 right-0 z-50"
          initial={{ y: -100, opacity: 0 }}
          animate={{
            y: isHeaderCompact ? 0 : -100,
            opacity: isHeaderCompact ? 1 : 0,
          }}
          transition={{
            duration: 0.4,
            ease: "easeInOut",
            type: "spring",
            stiffness: 300,
            damping: 30
          }}
          style={{
            top: 0,
            pointerEvents: isHeaderCompact ? "auto" : "none"
          }}
        >
          <div className="bg-slate-900 border-b border-white/10 shadow-2xl">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleBackPress}
                      className="p-2 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-200"
                    >
                      <ArrowLeft className="text-white" size={20} />
                    </motion.button>
                    <div>
                      <h1 className="text-xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                        Categories
                      </h1>
                      <p className="text-white/60 text-xs">Choose your category</p>
                    </div>
                  </div>

                  {/* Compact Search */}
                  <div className="flex-1 max-w-md mx-8">
                    <div className="bg-white/10 backdrop-blur-xl rounded-xl border border-white/20 hover:border-white/30 transition-all duration-200">
                      <div className="flex items-center gap-3 p-3">
                        <Search size={18} className="text-white/60" />
                        <input
                          type="text"
                          placeholder="Search categories..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-sm"
                        />
                        {searchQuery && (
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={clearSearch}
                            className="p-1 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-200"
                          >
                            <X size={16} className="text-white/60" />
                          </motion.button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="relative z-10 pt-32 pb-16"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            {/* Back Button for non-compact header */}
            <motion.div
              animate={{
                opacity: isHeaderCompact ? 0 : 1,
                height: isHeaderCompact ? 0 : "auto",
              }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden mb-8"
            >
              <motion.button
                whileHover={{ scale: 1.05, x: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleBackPress}
                className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 backdrop-blur-xl rounded-xl border border-white/20 text-white transition-all duration-200"
              >
                <ArrowLeft size={20} />
                <span>Back to Home</span>
              </motion.button>
            </motion.div>

            {/* Main Title */}
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="mb-8"
            >
              <h1 className="text-5xl md:text-6xl font-bold text-white mb-4">
                Choose Your
                <span className="bg-gradient-to-r pb-4 from-orange-400 via-pink-400 to-purple-400 bg-clip-text text-transparent block">
                  Category
                </span>
                <motion.span
                  animate={{ rotate: [0, 14, -8, 14, -4, 10, 0] }}
                  transition={{ duration: 2.5, repeat: Infinity, repeatDelay: 3 }}
                  className="inline-block ml-4"
                >
                  🛍️
                </motion.span>
              </h1>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="flex items-center justify-center gap-3 text-white/80 text-lg"
              >
                <MapPin size={20} className="text-orange-400" />
                <span>Discover amazing suppliers in Nablus, Palestine</span>
              </motion.div>
            </motion.div>

            {/* Stats Row */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="grid grid-cols-3 gap-8 mb-12"
            >
              <motion.div
                className="text-center"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-4xl font-bold text-white mb-2">{webCategories.length}+</div>
                <div className="text-white/70 text-lg">Categories</div>
              </motion.div>
              <motion.div
                className="text-center"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-4xl font-bold text-white mb-2">4.8★</div>
                <div className="text-white/70 text-lg">Average Rating</div>
              </motion.div>
              <motion.div
                className="text-center"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-4xl font-bold text-white mb-2">Fast</div>
                <div className="text-white/70 text-lg">Delivery</div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Content Section */}
        <div className="px-4 sm:px-6 lg:px-8 pb-20">
          {/* Premium Search Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="max-w-4xl mx-auto mb-16"
          >
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl overflow-hidden">
                <div className="flex items-center gap-4 p-6">
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-3 bg-gradient-to-r from-orange-500 to-pink-600 rounded-xl"
                  >
                    <Search size={24} className="text-white" />
                  </motion.div>
                  <input
                    type="text"
                    placeholder="Search categories, suppliers, or products…"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 bg-transparent text-white placeholder-white/60 outline-none text-lg font-medium"
                  />
                  {searchQuery.trim() && (
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={clearSearch}
                      className="p-2 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-200"
                    >
                      <X size={20} className="text-white" />
                    </motion.button>
                  )}
                </div>

                {/* Search suggestions */}
                {!searchQuery && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="px-6 pb-4"
                  >
                    <div className="flex flex-wrap gap-2">
                      {webCategories.slice(0, 4).map((category, index) => (
                        <motion.button
                          key={category.key}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.1 * index }}
                          onClick={() => setSearchQuery(category.label)}
                          className="px-3 py-1 bg-white/10 hover:bg-white/20 rounded-full text-white/80 text-sm transition-all duration-200"
                        >
                          {category.label}
                        </motion.button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Search Results - Positioned immediately after search section */}
          {showSearchResults && searchResults ? (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4 }}
              className="max-w-7xl mx-auto mb-8 mt-4"
            >
              {/* Search Summary - Always visible when searching or have results */}
              {searchQuery.trim() && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                  className="text-center mb-4"
                >
                  {isSearching ? (
                    <div className="flex items-center justify-center py-4">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-6 h-6 border-2 border-white/20 border-t-white rounded-full mr-3"
                      />
                      <span className="text-white/80">Searching for "{searchQuery}"...</span>
                    </div>
                  ) : searchResults ? (
                    <>
                      <h3 className="text-white text-xl font-semibold mb-1">
                        Results for "{searchQuery}"
                      </h3>
                      <p className="text-white/60 text-sm">
                        {searchResults.total} {searchResults.total === 1 ? 'result' : 'results'} found
                      </p>
                    </>
                  ) : null}
                </motion.div>
              )}

              {!isSearching && searchResults && (
                <>

                  {/* Categories Results */}
                  {searchResults.categories.length > 0 && (
                    <div className="mb-8">
                      <h4 className="text-xl font-semibold text-white/90 mb-4 flex items-center gap-2">
                        <Store size={20} className="text-green-400" />
                        Categories ({searchResults.categories.length})
                      </h4>
                      <div className="grid gap-4">
                        {searchResults.categories.map((category: any, index: number) => (
                          <motion.div
                            key={category.id}
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            whileHover={{ scale: 1.02, x: 10 }}
                            onClick={() => window.location.href = category.route}
                            className="bg-gradient-to-r from-green-600 to-emerald-700 backdrop-blur-xl rounded-2xl p-6 border border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300 shadow-green-500/25 shadow-xl hover:shadow-2xl"
                          >
                            <div className="flex items-center gap-6">
                              <div className="p-4 bg-white/20 backdrop-blur-sm rounded-2xl">
                                <Store className="text-white" size={32} />
                              </div>
                              <div className="flex-1">
                                <h4 className="text-white font-bold text-xl mb-1">{category.title}</h4>
                                <p className="text-white/80 mb-2">{category.description || 'Browse suppliers in this category'}</p>
                                <span className="inline-block px-3 py-1 bg-green-400 text-green-900 rounded-full text-xs font-bold">
                                  Category
                                </span>
                              </div>
                              <ChevronRight className="text-white/60" size={24} />
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Suppliers Results */}
                  {searchResults.suppliers.length > 0 && (
                    <div className="mb-8">
                      <h4 className="text-xl font-semibold text-white/90 mb-4 flex items-center gap-2">
                        <Store size={20} className="text-blue-400" />
                        Suppliers ({searchResults.suppliers.length})
                      </h4>
                      <div className="grid gap-4">
                        {searchResults.suppliers.map((supplier: any, index: number) => (
                          <motion.div
                            key={supplier.id}
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            whileHover={{ scale: 1.02, x: 10 }}
                            onClick={() => window.location.href = supplier.route}
                            className="bg-gradient-to-r from-blue-600 to-indigo-700 backdrop-blur-xl rounded-2xl p-6 border border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300 shadow-blue-500/25 shadow-xl hover:shadow-2xl"
                          >
                            <div className="flex items-center gap-6">
                              <div className="p-4 bg-white/20 backdrop-blur-sm rounded-2xl">
                                <Store className="text-white" size={32} />
                              </div>
                              <div className="flex-1">
                                <h4 className="text-white font-bold text-xl mb-1">{supplier.title}</h4>
                                <p className="text-white/80 mb-2">{supplier.subtitle}</p>
                                <span className="inline-block px-3 py-1 bg-blue-400 text-blue-900 rounded-full text-xs font-bold">
                                  Supplier
                                </span>
                              </div>
                              <ChevronRight className="text-white/60" size={24} />
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Products Results */}
                  {searchResults.products.length > 0 && (
                    <div className="mb-8">
                      <h4 className="text-xl font-semibold text-white/90 mb-4 flex items-center gap-2">
                        <ShoppingCart size={20} className="text-purple-400" />
                        Products ({searchResults.products.length})
                      </h4>
                      <div className="grid gap-4">
                        {searchResults.products.map((product: any, index: number) => (
                          <motion.div
                            key={product.id}
                            initial={{ opacity: 0, x: -30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            whileHover={{ scale: 1.02, x: 10 }}
                            onClick={() => window.location.href = product.route}
                            className="bg-gradient-to-r from-purple-600 to-pink-700 backdrop-blur-xl rounded-2xl p-6 border border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300 shadow-purple-500/25 shadow-xl hover:shadow-2xl"
                          >
                            <div className="flex items-center gap-6">
                              <div className="p-4 bg-white/20 backdrop-blur-sm rounded-2xl">
                                <ShoppingCart className="text-white" size={32} />
                              </div>
                              <div className="flex-1">
                                <h4 className="text-white font-bold text-xl mb-1">{product.title}</h4>
                                <p className="text-white/80 mb-2">{product.subtitle}</p>
                                <span className="inline-block px-3 py-1 bg-purple-400 text-purple-900 rounded-full text-xs font-bold">
                                  Product
                                </span>
                              </div>
                              <ChevronRight className="text-white/60" size={24} />
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* No Results */}
                  {searchResults.total === 0 && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5 }}
                      className="flex flex-col items-center justify-center py-20"
                    >
                      <motion.div
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                        className="p-6 bg-white/10 rounded-3xl mb-6"
                      >
                        <Search className="text-white/60" size={64} />
                      </motion.div>
                      <h3 className="text-white text-2xl font-bold mb-2">No results found</h3>
                      <p className="text-white/60 text-center max-w-md">
                        Try searching for something else or browse our available categories
                      </p>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={clearSearch}
                        className="mt-4 px-6 py-3 bg-gradient-to-r from-orange-500 to-pink-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-200"
                      >
                        Clear Search
                      </motion.button>
                    </motion.div>
                  )}
                </>
              )}
            </motion.div>
          ) : null}

          {/* Categories Grid */}
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="space-y-8"
            >
              {!showSearchResults && (
                <div className="text-center mb-8">
                  <motion.h2
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    className="text-4xl font-bold bg-gradient-to-r from-white via-orange-100 to-pink-100 bg-clip-text text-transparent mb-4"
                  >
                    Premium Categories
                  </motion.h2>
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7 }}
                    className="text-white/70 text-lg max-w-2xl mx-auto"
                  >
                    Explore our curated selection of top-quality suppliers across different categories
                  </motion.p>
                </div>
              )}

              {/* Categories Display */}
              {filteredCategories.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                  className="flex flex-col items-center justify-center py-20"
                >
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-6 bg-white/10 rounded-3xl mb-6"
                  >
                    <Search className="text-white/60" size={64} />
                  </motion.div>
                  <h3 className="text-white text-2xl font-bold mb-2">No categories available</h3>
                  <p className="text-white/60 text-center max-w-md">
                    Categories will appear here once they are added to the system
                  </p>
                </motion.div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                  {filteredCategories.map((category, index) => {
                    const Icon = category.icon;
                    return (
                      <motion.div
                        key={category.key}
                        initial={{ opacity: 0, scale: 0.8, y: 30 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        transition={{
                          duration: 0.8,
                          delay: index * 0.15,
                          type: "spring",
                          stiffness: 120
                        }}
                        whileHover={{
                          scale: 1.03,
                          y: -8,
                          transition: { duration: 0.3 }
                        }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => handleCategoryPress(category.route)}
                        className="group cursor-pointer"
                      >
                        {/* Premium Category Card */}
                        <div className={`relative rounded-3xl p-8 bg-gradient-to-br ${category.bgGradient} ${category.shadowColor} shadow-2xl hover:shadow-3xl transition-all duration-500 border border-white/10 overflow-hidden`}>
                          {/* Background Pattern */}
                          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
                          <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>

                          {/* Badge */}
                          <motion.div
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.3 + index * 0.1 }}
                            className={`absolute top-4 right-4 px-3 py-1 ${category.badgeColor} rounded-full text-xs font-bold`}
                          >
                            {category.badge}
                          </motion.div>

                          {/* Special glow effect for popular category */}
                          {category.special && (
                            <motion.div
                              animate={{ opacity: [0.5, 1, 0.5] }}
                              transition={{ duration: 2, repeat: Infinity }}
                              className="absolute inset-0 bg-gradient-to-r from-orange-400/20 via-pink-400/20 to-orange-400/20 rounded-3xl"
                            />
                          )}

                          <div className="relative z-10">
                            {/* Icon */}
                            <motion.div
                              whileHover={{ rotate: 360 }}
                              transition={{ duration: 0.6 }}
                              className="flex justify-center mb-6"
                            >
                              <div className="p-4 bg-white/20 backdrop-blur-sm rounded-2xl">
                                <Icon
                                  className="group-hover:scale-110 transition-transform duration-300 text-white"
                                  size={48}
                                />
                              </div>
                            </motion.div>

                            {/* Content */}
                            <div className="text-center mb-6">
                              <h3 className="text-white font-bold text-2xl mb-2 leading-tight">
                                {category.label}
                              </h3>
                              <p className="text-white/80 text-sm mb-4 leading-relaxed">
                                {category.subtitle}
                              </p>

                              {/* Stats */}
                              <div className="grid grid-cols-3 gap-4 mb-4">
                                <div className="text-center">
                                  <div className="text-white font-bold text-lg">{category.stats.suppliers}</div>
                                  <div className="text-white/60 text-xs">Suppliers</div>
                                </div>
                                <div className="text-center">
                                  <div className="text-white font-bold text-lg flex items-center justify-center gap-1">
                                    {category.stats.avgRating}
                                    <Star size={14} className="text-yellow-400 fill-current" />
                                  </div>
                                  <div className="text-white/60 text-xs">Rating</div>
                                </div>
                                <div className="text-center">
                                  <div className="text-white font-bold text-lg flex items-center justify-center gap-1">
                                    <Clock size={14} />
                                    {category.stats.deliveryTime.split(' ')[0]}
                                  </div>
                                  <div className="text-white/60 text-xs">Delivery</div>
                                </div>
                              </div>

                              {/* Action indicator */}
                              <motion.div
                                whileHover={{ x: 5 }}
                                className="flex items-center justify-center gap-2 text-white/60 text-sm"
                              >
                                <span>Explore Suppliers</span>
                                <ChevronRight size={16} />
                              </motion.div>
                            </div>

                            {/* Special effects for popular category */}
                            {category.special && (
                              <motion.div
                                animate={{ scale: [1, 1.05, 1] }}
                                transition={{ duration: 2, repeat: Infinity }}
                                className="flex items-center justify-center gap-2 text-white/90 text-xs bg-white/10 rounded-full py-2 px-4"
                              >
                                <Crown size={14} className="animate-pulse text-yellow-400" />
                                <span className="font-medium">Most Popular</span>
                                <Sparkles size={14} className="animate-pulse text-yellow-400" />
                              </motion.div>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              )}
            </motion.div>
          </div>
        </div>

        {/* Premium Features Banner */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
          className="fixed bottom-8 left-8 z-30 max-w-sm"
        >
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 border border-white/20 shadow-xl">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-orange-400 to-pink-500 rounded-xl">
                <Shield className="text-white" size={20} />
              </div>
              <div>
                <p className="text-white font-semibold text-sm">Trusted Platform</p>
                <p className="text-white/70 text-xs">Verified suppliers only</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Floating Action Button for Quick Access */}
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 1, type: "spring", stiffness: 200 }}
          className="fixed bottom-8 right-8 z-40"
        >
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => navigate('/customer/home')}
            className="w-16 h-16 bg-gradient-to-r from-orange-500 to-pink-600 rounded-full shadow-2xl flex items-center justify-center group"
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="absolute inset-0 bg-gradient-to-r from-orange-400 to-pink-500 rounded-full opacity-75 blur-lg"
            />
            <Store className="text-white relative z-10 group-hover:scale-110 transition-transform" size={28} />
          </motion.button>
        </motion.div>
      </div>
    </>
  );
};

export default SupplierCategoriesPage;